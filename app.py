from flask import Flask, request, jsonify
import cv2
import pytesseract
import numpy as np

# 確保你的 pytesseract 執行檔路徑正確
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

app = Flask(__name__)

@app.route('/api/recognize_license_plate', methods=['POST'])
def recognize_license_plate():
    # 檢查是否有檔案上傳
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400

    image_file = request.files['image']
    
    # 將圖片讀取為 OpenCV 格式
    np_img = np.fromstring(image_file.read(), np.uint8)
    img = cv2.imdecode(np_img, cv2.IMREAD_COLOR)

    # 簡單的圖像處理，這裡只進行灰階化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 使用 Tesseract 進行 OCR 辨識
    try:
        # Tesseract 的 config 可以幫助提高辨識準確度
        config = '--psm 8 --oem 3 -l eng'  # psm 8 for single word, oem 3 for legacy + LSTM
        text = pytesseract.image_to_string(gray, config=config)
        
        # 過濾掉多餘的空白和特殊字元
        license_plate = "".join(filter(str.isalnum, text.upper()))

        if license_plate:
            return jsonify({'license_plate': license_plate}), 200
        else:
            return jsonify({'error': 'Could not recognize license plate'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)