<!DOCTYPE html>
<html>
<head>
    <title>車牌辨識系統</title>
</head>
<body>
    <h2>上傳圖片進行車牌辨識</h2>
    <form id="uploadForm">
        <input type="file" id="imageInput" accept="image/*">
        <button type="submit">開始辨識</button>
    </form>
    
    <h3>辨識結果：</h3>
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('請先選擇一個圖片檔案！');
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            const resultDiv = document.getElementById('result');
            resultDiv.innerText = '辨識中...';

            try {
                const response = await fetch('/api/recognize_license_plate', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `**辨識成功！** 車牌號碼：<br><h1>${data.license_plate}</h1>`;
                } else {
                    resultDiv.innerText = `辨識失敗：${data.error}`;
                }
            } catch (error) {
                resultDiv.innerText = '發生錯誤，請稍後再試。';
                console.error('Error:', error);
            }
        });
    </script>
</body>
</html>